'use client';

import React, { createContext, useContext, useState, useCallback, useMemo, useEffect, ReactNode } from 'react';
import { useSession } from 'next-auth/react';

export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  isCompleted: boolean;
}

export interface SchoolData {
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  registeredNumber?: string;
}

export interface BrandingData {
  primaryColor?: string;
  theme?: string;
}

export interface ProfileData {
  bio?: string;
  specialization?: string;
  experience?: string;
}

export interface OnboardingState {
  currentStep: string;
  steps: OnboardingStep[];
  schoolData: SchoolData | null;
  brandingData: BrandingData | null;
  profileData: ProfileData | null;
  isLoading: boolean;
  error: string | null;
}

export interface OnboardingContextType {
  state: OnboardingState;
  isInitialized: boolean;
  setCurrentStep: (step: string) => void;
  setSchoolData: (data: SchoolData) => void;
  setBrandingData: (data: BrandingData) => void;
  setProfileData: (data: ProfileData) => void;
  markStepCompleted: (stepId: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  resetOnboarding: () => void;
  canNavigateToStep: (stepId: string) => boolean;
  getNextStep: () => string | null;
  getPreviousStep: () => string | null;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

const ONBOARDING_STEPS: OnboardingStep[] = [
  {
    id: 'school-details',
    title: 'School Details',
    description: 'Set up your school information',
    isCompleted: false,
  },
  {
    id: 'branding',
    title: 'Branding',
    description: 'Customize your school appearance',
    isCompleted: false,
  },
  {
    id: 'profile',
    title: 'Profile',
    description: 'Complete your teacher profile',
    isCompleted: false,
  },
  {
    id: 'complete',
    title: 'Complete',
    description: 'Finish setup',
    isCompleted: false,
  },
];

const initialState: OnboardingState = {
  currentStep: 'school-details',
  steps: ONBOARDING_STEPS,
  schoolData: null,
  brandingData: null,
  profileData: null,
  isLoading: false,
  error: null,
};

export function OnboardingProvider({ children }: { children: ReactNode }) {
  const { data: session } = useSession();
  const [state, setState] = useState<OnboardingState>(initialState);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize onboarding state based on session data
  useEffect(() => {
    if (session?.user && !isInitialized) {
      const hasSchool = session.user.schoolId && session.user.school;

      if (hasSchool) {
        // User already has school data, mark school-details as completed
        // and set current step to branding
        setState(prev => ({
          ...prev,
          currentStep: 'branding',
          steps: prev.steps.map(step =>
            step.id === 'school-details' ? { ...step, isCompleted: true } : step
          ),
          schoolData: {
            name: session.user.school?.name || '',
            address: session.user.school?.address || '',
            phone: session.user.school?.phoneNumber || '',
            registeredNumber: session.user.school?.registeredNumber || '',
            email: session.user.school?.email || '',
          }
        }));
      }

      setIsInitialized(true);
    }
  }, [session, isInitialized]);

  const setCurrentStep = useCallback((step: string) => {
    setState(prev => ({
      ...prev,
      currentStep: step,
      error: null,
    }));
  }, []);

  const setSchoolData = useCallback((data: SchoolData) => {
    setState(prev => ({
      ...prev,
      schoolData: data,
    }));
  }, []);

  const setBrandingData = useCallback((data: BrandingData) => {
    setState(prev => ({
      ...prev,
      brandingData: data,
    }));
  }, []);

  const setProfileData = useCallback((data: ProfileData) => {
    setState(prev => ({
      ...prev,
      profileData: data,
    }));
  }, []);

  const markStepCompleted = useCallback((stepId: string) => {
    setState(prev => ({
      ...prev,
      steps: prev.steps.map(step =>
        step.id === stepId ? { ...step, isCompleted: true } : step
      ),
    }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({
      ...prev,
      isLoading: loading,
    }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({
      ...prev,
      error,
    }));
  }, []);

  const resetOnboarding = useCallback(() => {
    setState(initialState);
  }, []);

  const canNavigateToStep = useCallback((stepId: string) => {
    const stepIndex = state.steps.findIndex(step => step.id === stepId);
    const currentStepIndex = state.steps.findIndex(step => step.id === state.currentStep);

    // Can navigate to current step or any previous completed step
    if (stepIndex <= currentStepIndex) {
      return true;
    }

    // Can navigate to next step if current step is completed
    if (stepIndex === currentStepIndex + 1) {
      const currentStep = state.steps[currentStepIndex];
      return currentStep.isCompleted;
    }

    return false;
  }, [state.steps, state.currentStep]);

  const getNextStep = useCallback((): string | null => {
    const currentIndex = state.steps.findIndex(step => step.id === state.currentStep);
    if (currentIndex < state.steps.length - 1) {
      return state.steps[currentIndex + 1].id;
    }
    return null;
  }, [state.steps, state.currentStep]);

  const getPreviousStep = useCallback((): string | null => {
    const currentIndex = state.steps.findIndex(step => step.id === state.currentStep);
    if (currentIndex > 0) {
      return state.steps[currentIndex - 1].id;
    }
    return null;
  }, [state.steps, state.currentStep]);

  const contextValue = useMemo<OnboardingContextType>(() => ({
    state,
    isInitialized,
    setCurrentStep,
    setSchoolData,
    setBrandingData,
    setProfileData,
    markStepCompleted,
    setLoading,
    setError,
    resetOnboarding,
    canNavigateToStep,
    getNextStep,
    getPreviousStep,
  }), [
    state,
    isInitialized,
    setCurrentStep,
    setSchoolData,
    setBrandingData,
    setProfileData,
    markStepCompleted,
    setLoading,
    setError,
    resetOnboarding,
    canNavigateToStep,
    getNextStep,
    getPreviousStep,
  ]);

  return (
    <OnboardingContext.Provider value={contextValue}>
      {children}
    </OnboardingContext.Provider>
  );
}

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
}
