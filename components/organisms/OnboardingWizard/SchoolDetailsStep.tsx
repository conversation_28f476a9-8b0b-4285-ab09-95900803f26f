'use client';

import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useOnboarding } from './OnboardingContext';
import { SchoolCreationForm } from '@/components/organisms/SchoolCreationForm';
import { Button } from '@/components/atoms/Button/Button';

export default function SchoolDetailsStep() {
  const router = useRouter();
  const { data: session } = useSession();
  const {
    state,
    isInitialized,
    setSchoolData,
    markStepCompleted,
    setError,
    getNextStep
  } = useOnboarding();
  const { isLoading } = state;

  // Check if user already has school data and auto-advance
  useEffect(() => {
    // Wait for context to be initialized before making decisions
    if (!isInitialized) {
      console.log('Waiting for onboarding context to initialize...');
      return;
    }

    console.log('Session data:', session);
    console.log('Current onboarding state:', state);

    // Check if this step is already completed (from context initialization)
    const schoolDetailsStep = state.steps.find(step => step.id === 'school-details');
    const isStepCompleted = schoolDetailsStep?.isCompleted;

    if (session?.user?.schoolId && session?.user?.school && !isStepCompleted) {
      console.log('✅ User already has school data, marking step completed and advancing');

      // Mark this step as completed
      markStepCompleted('school-details');

      // Set school data in context if not already set
      if (!state.schoolData) {
        setSchoolData({
          name: session.user.school.name,
          address: session.user.school.address || '',
          phone: session.user.school.phoneNumber || '',
          registeredNumber: session.user.school.registeredNumber || '',
          email: session.user.school.email || '',
        });
      }

      // Navigate to next step
      const nextStep = getNextStep();
      if (nextStep) {
        router.push(`/onboarding/${nextStep}`);
      }
    } else if (isStepCompleted) {
      console.log('✅ School details step already completed, advancing to next step');
      // Navigate to next step if this step is already completed
      const nextStep = getNextStep();
      if (nextStep) {
        router.push(`/onboarding/${nextStep}`);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session?.user?.schoolId, session?.user?.school, isInitialized, state.steps, state.schoolData]);

  const handleSchoolCreated = () => {
    markStepCompleted('school-details');

    // Force a router refresh to ensure middleware gets updated session
    router.refresh();

    // Navigate to next step with a small delay to ensure session is updated
    const nextStep = getNextStep();
    if (nextStep) {
      setTimeout(() => {
        router.push(`/onboarding/${nextStep}`);
      }, 1000); // Give time for session to propagate
    }
  };

  const handleSchoolCreationError = (error: string) => {
    setError(error);
  };

  const handleSkip = () => {
    // Allow skipping school creation for now, but mark as incomplete
    const nextStep = getNextStep();
    if (nextStep) {
      router.push(`/onboarding/${nextStep}`);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="space-y-4">
        {/* School Creation Form */}
        <SchoolCreationForm
          onSuccess={handleSchoolCreated}
          onError={handleSchoolCreationError}
        />

        {/* Skip Option */}
        <div className="flex justify-between items-center mt-8 pt-3 border-t border-gray-200">
          <Button
            variant="ghost"
            onClick={handleSkip}
            disabled={isLoading}
            className="text-gray-600 hover:text-gray-800"
          >
            Skip for now
          </Button>

          <div className="text-sm text-gray-500">
            Complete later in dashboard
          </div>
        </div>
      </div>
    </motion.div>
  );
}
