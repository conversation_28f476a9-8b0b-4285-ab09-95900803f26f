'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { useOnboarding } from './OnboardingContext';
import { Button } from '@/components/atoms/Button/Button';
import { Icon } from '@/components/atoms';

const THEME_OPTIONS = [
  { id: 'blue', name: 'Ocean Blue', color: '#3B82F6', description: 'Professional and trustworthy' },
  { id: 'green', name: 'Forest Green', color: '#10B981', description: 'Growth and harmony' },
  { id: 'purple', name: 'Royal Purple', color: '#8B5CF6', description: 'Creative and inspiring' },
  { id: 'orange', name: 'Sunset Orange', color: '#F59E0B', description: 'Energetic and warm' },
  { id: 'red', name: 'Cherry Red', color: '#EF4444', description: 'Bold and passionate' },
  { id: 'teal', name: '<PERSON><PERSON>', color: '#14B8A6', description: 'Modern and fresh' },
];

export default function BrandingStep() {
  const router = useRouter();
  const { 
    state, 
    setBrandingData, 
    markStepCompleted, 
    getPreviousStep,
    getNextStep 
  } = useOnboarding();
  
  const [selectedTheme, setSelectedTheme] = useState<string>('blue');
  const [isLoading, setIsLoading] = useState(false);

  const handleBack = () => {
    const previousStep = getPreviousStep();
    if (previousStep) {
      router.push(`/onboarding/${previousStep}`);
    }
  };

  const handleNext = async () => {
    setIsLoading(true);
    
    try {
      // Store branding data
      const selectedThemeData = THEME_OPTIONS.find(theme => theme.id === selectedTheme);
      setBrandingData({
        primaryColor: selectedThemeData?.color,
        theme: selectedTheme,
      });

      // Mark step as completed
      markStepCompleted('branding');

      // Navigate to next step
      const nextStep = getNextStep();
      if (nextStep) {
        router.push(`/onboarding/${nextStep}`);
      }
    } catch (error) {
      console.error('Error saving branding data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkip = () => {
    // Skip branding step
    const nextStep = getNextStep();
    if (nextStep) {
      router.push(`/onboarding/${nextStep}`);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      {/* Card Container */}
      <div className="card bg-base-100 shadow-xl border border-gray-100">
        <div className="card-body p-8">
          <h2 className="card-title text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3 justify-center">
            <Icon variant="star" size={8} className="text-primary" />
            Choose Your School Theme
          </h2>

          <div className="space-y-6">
            {/* Theme Selection */}
            <div className="space-y-4">

              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {THEME_OPTIONS.map((theme) => (
                  <motion.div
                    key={theme.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`cursor-pointer p-4 rounded-xl border-2 transition-all duration-300 shadow-md hover:shadow-lg ${
                      selectedTheme === theme.id
                        ? 'border-primary bg-primary/10 ring-2 ring-primary/20'
                        : 'border-gray-200 hover:border-gray-300 bg-white hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedTheme(theme.id)}
                  >
                    <div className="flex flex-col items-center space-y-3">
                      <div
                        className="w-12 h-12 rounded-full shadow-lg border-2 border-white"
                        style={{ backgroundColor: theme.color }}
                      ></div>
                      <div className="text-center">
                        <div className="font-semibold text-sm text-gray-800">{theme.name}</div>
                        <div className="text-xs text-gray-500 mt-1">{theme.description}</div>
                      </div>
                      {selectedTheme === theme.id && (
                        <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                          <Icon variant="check" size={3} className="text-white" />
                        </div>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
        </div>

            {/* Navigation */}
            <div className="flex justify-between items-center pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={handleBack}
                disabled={isLoading}
                className="btn btn-outline btn-lg flex items-center gap-2"
              >
                <Icon variant="arrow-left" size={4} />
                <span>Back</span>
              </button>

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={handleSkip}
                  disabled={isLoading}
                  className="btn btn-ghost btn-lg text-gray-600 hover:text-gray-800"
                >
                  Skip
                </button>

                <button
                  type="button"
                  onClick={handleNext}
                  disabled={isLoading}
                  className="btn btn-primary btn-lg flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 border-0 shadow-lg"
                >
                  {isLoading ? (
                    <>
                      <span className="loading loading-spinner loading-sm"></span>
                      <span>Saving...</span>
                    </>
                  ) : (
                    <>
                      <span>Continue</span>
                      <Icon variant="arrow-right" size={4} />
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
