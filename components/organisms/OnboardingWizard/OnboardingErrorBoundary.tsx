'use client';

import React from 'react';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';

interface OnboardingErrorBoundaryProps {
  children: React.ReactNode;
}

interface OnboardingErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class OnboardingErrorBoundary extends React.Component<
  OnboardingErrorBoundaryProps,
  OnboardingErrorBoundaryState
> {
  constructor(props: OnboardingErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): OnboardingErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Onboarding Error Boundary caught an error:', error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
          <div className="max-w-md mx-auto text-center p-8">
            <div className="relative">
              {/* Gradient Background */}
              <div className="absolute inset-0 bg-gradient-to-br from-red-50 via-white to-orange-50 rounded-2xl"></div>
              
              {/* Glass Effect */}
              <div className="relative bg-white/70 backdrop-blur-sm border border-white/40 rounded-2xl p-8 shadow-lg">
                {/* Error Icon */}
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center">
                    <AlertTriangle size={24} className="text-white" />
                  </div>
                </div>

                {/* Error Message */}
                <h2 className="text-2xl font-bold text-gray-800 mb-4">
                  Oops! Something went wrong
                </h2>
                <p className="text-gray-600 mb-6">
                  We encountered an unexpected error during the onboarding process. 
                  Don't worry, your progress has been saved.
                </p>

                {/* Error Details (in development) */}
                {process.env.NODE_ENV === 'development' && this.state.error && (
                  <div className="bg-gray-100 rounded-lg p-4 mb-6 text-left">
                    <p className="text-sm font-medium text-gray-700 mb-2">Error Details:</p>
                    <p className="text-xs text-gray-600 font-mono">
                      {this.state.error.message}
                    </p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="space-y-3">
                  <Button
                    onClick={this.handleRetry}
                    variant="primary"
                    className="w-full flex items-center justify-center space-x-2"
                  >
                    <RefreshCw size={16} />
                    <span>Try Again</span>
                  </Button>

                  <Button
                    onClick={this.handleGoHome}
                    variant="ghost"
                    className="w-full flex items-center justify-center space-x-2 text-gray-600 hover:text-gray-800"
                  >
                    <Home size={16} />
                    <span>Go to Dashboard</span>
                  </Button>
                </div>

                {/* Help Text */}
                <p className="text-sm text-gray-500 mt-6">
                  If the problem persists, please contact support or try completing 
                  the setup from your dashboard.
                </p>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
